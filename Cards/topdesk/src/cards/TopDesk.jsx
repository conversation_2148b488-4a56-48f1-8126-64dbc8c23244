import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import "./styles.css";
import $ from 'jquery';
 
import {
    makeStyles,
    Card,
    CardContent,
    Typography,
    CircularProgress,
    Button
} from '@ellucian/react-design-system/core';
import { useUserInfo } from '@ellucian/experience-extension-utils';

// Define API configuration directly without using process.env
const TOPDESK_API = {
  baseUrl: 'https://ocadu-pilot.topdesk.net/tas/api',
  username: 'mcapioperator', // Replace with actual username
  password: 'vcubx-xuxxa-crxhr-y2knr-5jnft'  // Replace with actual password
};

// TESTING FLAG - Set to false to use the actual user's email
const USE_TEST_EMAIL = true;
const TEST_EMAIL = "<EMAIL>";

const useStyles = makeStyles(theme => ({
    root: {
        height: '100%',
        padding: theme.spacing(2),
        paddingRight: theme.spacing(4), 
        overflowY: 'auto',
        paddingBottom: '40%'
    },
    card: {
        marginBottom: theme.spacing(2),
        borderLeft: '8px solid #cccccc' // Default grey border
    },
    cardResponded: {
        padding: theme.spacing(2),
        borderLeft: '8px solid #3CAB49' // Green border for responded tickets
    },
    cardClosed: {
        padding: theme.spacing(2),
        borderLeft: '8px solid #cccccc', // Grey border
        backgroundColor: '#a5a5a5', // Dark grey background for closed tickets
        opacity: 0.6,
        color: '#000'
    },
    cardRespondedClosed: {
        padding: theme.spacing(2),
        borderLeft: '8px solid #000000',
        backgroundColor: '#a5a5a5', // Dark grey background for closed tickets
        opacity: 0.6,
        color: '#000'
    },
    cardNotRespondedClosed: {
        padding: theme.spacing(2),
        borderLeft: '8px solid #000000',
        backgroundColor: '#a5a5a5', // Dark grey background for closed tickets
        opacity: 0.6,
        color: '#000'
    },
    cardContent: {
        padding: theme.spacing(1),
        display: 'flex',
        flexDirection: 'column',
        height: '100%'
    },
    title: {
        fontSize: '1rem',
        fontWeight: 'bold'
    },
    cardHeader: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: theme.spacing(1)
    },
    ticketNumber: {
        fontSize: '0.875rem',
        color: theme.palette.text.secondary,
        fontWeight: 'normal'
    },
    ticketInfo: {
        display: 'flex',
        justifyContent: 'flex-end',
        marginTop: theme.spacing(1)
    },
    bottomRow: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        marginTop: 'auto'
    },
    categoryText: {
        fontSize: '0.875rem',
        color: theme.palette.text.primary
    },
    dateText: {
        fontSize: '0.875rem',
        color: theme.palette.text.secondary,
        textAlign: 'right'
    },
    chip: {
        margin: theme.spacing(0.5)
    },
    loader: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%'
    },
    noTickets: {
        textAlign: 'center',
        padding: theme.spacing(4)
    },
    statusChips: {
        display: 'flex',
        flexWrap: 'wrap',
        marginTop: theme.spacing(1)
    },
    floatingMenu: {
        position: 'absolute',
        bottom: '0px',
        display: 'flex',
        justifyContent: 'center',
        gap: theme.spacing(2),
        marginTop: 'auto',
        marginLeft: '-0.5rem',
        padding: theme.spacing(4), // Consistent padding all around
        zIndex: 10,
        backgroundColor: '#ffffff', // White background
        borderRadius: theme.shape.borderRadius, // Rounded corners
        boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)', // Subtle shadow for elevation
        width: '100%', // Full width
        boxSizing: 'border-box' // Ensure padding is included in width calculation
    },
    allRequestsButton: {
        width: '50%',
        backgroundColor: '#000000',
        color: '#ffffff',
        '&:hover': {
            backgroundColor: '#333333',
        }
    },
    newRequestButton: {
        backgroundColor: '#1AB0F6', // Light blue
        color: '#ffffff',
        '&:hover': {
            backgroundColor: '#0D8BC7',
        }
    }
}));

// Add error boundary to catch rendering errors
class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error("TopDesk card error:", error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div style={{ padding: 20, textAlign: 'center' }}>
                    <Typography color="error">
                        Something went wrong loading the TopDesk card.
                    </Typography>
                    <Typography variant="caption" style={{ display: 'block', marginTop: 10 }}>
                        Error: {this.state.error && this.state.error.toString()}
                    </Typography>
                </div>
            );
        }

        return this.props.children;
    }
}

// Add PropTypes validation for ErrorBoundary
ErrorBoundary.propTypes = {
    children: PropTypes.node.isRequired
};

function TopDeskCard() {
    const classes = useStyles();
    const [tickets, setTickets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    
    // Get user info from Ellucian Experience
    const userInfo = useUserInfo() || {};
    
    // Use test email or actual user email based on the testing flag
    const email = USE_TEST_EMAIL ? TEST_EMAIL : (userInfo.email || '');
    
    // Handle button clicks
    const handleAllRequestsClick = () => {
        window.open('https://ocadu.topdesk.net/tas/public/ssp/content/page/myrequests', '_blank');
    };
    
    const handleNewRequestClick = () => {
        window.open('https://ocadu.topdesk.net/tas/public/ssp/content/serviceflow?unid=fd95cd0cb89642448c3d393c7b5ccc0d', '_blank');
    };
    
    // Handle ticket card click
    const handleTicketClick = (ticketId) => {
        if (ticketId) {
            window.open(`https://ocadu-pilot.topdesk.net/tas/public/ssp/content/detail/incident?unid=${ticketId}`, '_blank');
        }
    };
    
    useEffect(() => {
        if (!email) {
            setLoading(false);
            setError('User email not available');
            return;
        }
        
        console.log(`Fetching tickets for email: ${email}`);
        
        // Fetch tickets from TopDesk API
        const fetchTickets = async () => {
            try {
                setLoading(true);
                
                // Create Basic Auth token
                const basicAuth = 'Basic ' + btoa(`${TOPDESK_API.username}:${TOPDESK_API.password}`);
                
                const response = await $.ajax({
                    url: `${TOPDESK_API.baseUrl}/incidents?query=caller.email=='${email}'`,
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'Authorization': basicAuth,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('API Response:', response);
                
                // Ensure we have an array of tickets
                const ticketData = Array.isArray(response) ? response : [];
                
                // Log the first ticket to see its structure
                if (ticketData.length > 0) {
                    console.log('First ticket:', ticketData[0]);
                    console.log('Processing status type:', typeof ticketData[0].processingStatus);
                }
                
                setTickets(ticketData);
                setLoading(false);
            } catch (err) {
                console.error('Error fetching tickets:', err);
                setError('Failed to load tickets. Please try again later.');
                setLoading(false);
            }
        };
        
        fetchTickets();
    }, [email]);
    
    // Format date to a more readable format
    const formatDate = (dateString) => {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (e) {
            return dateString;
        }
    };
    
    // Process ticket request/subject text
    const processSubject = (requestText) => {
        if (!requestText) return 'No Subject';
        
        // If the text contains "subject:", trim all characters before it
        let processedText = requestText;
        const subjectIndex = requestText.toLowerCase().indexOf('subject:');
        
        if (subjectIndex !== -1) {
            processedText = requestText.substring(subjectIndex + 8).trim(); // 8 is the length of "subject:"
        }
        
        // Truncate to 36 characters and add ellipsis if needed
        if (processedText.length > 36) {
            return processedText.substring(0, 36) + '...';
        }
        
        return processedText;
    };
    
    // Sort tickets: open first, then by responded status
    const sortedTickets = [...tickets].sort((a, b) => {
        // First sort by open/closed status (open first)
        const aIsClosed = a.processingStatus?.name?.toLowerCase() === 'closed';
        const bIsClosed = b.processingStatus?.name?.toLowerCase() === 'closed';
        
        if (aIsClosed !== bIsClosed) {
            return aIsClosed ? 1 : -1; // Open tickets first
        }
        
        // Then sort by responded status (responded first)
        const aHasResponded = a.respondedStatus === true;
        const bHasResponded = b.respondedStatus === true;
        
        if (aHasResponded !== bHasResponded) {
            return aHasResponded ? -1 : 1; // Responded tickets first
        }
        
        // Finally sort by creation date (newest first)
        return new Date(b.creationDate || 0) - new Date(a.creationDate || 0);
    });
    
    if (loading) {
        return (
            <div className={classes.loader}>
                <CircularProgress />
            </div>
        );
    }
    
    if (error) {
        return (
            <div className={classes.noTickets}>
                <Typography variant="body1" color="error">{error}</Typography>
            </div>
        );
    }
    
    if (!tickets || tickets.length === 0) {
        return (
            <div className={classes.noTickets}>
                <Typography variant="body1">No tickets found.</Typography>
            </div>
        );
    }
    
    return (
        <div className={classes.root}>
            {sortedTickets.map((ticket) => {
                // Determine which card style to use based on status
                const isClosed = ticket.processingStatus?.name?.toLowerCase() === 'closed';
                const hasResponded = ticket.respondedStatus === true;
                
                let cardClassName;
                if (isClosed && hasResponded) {
                    cardClassName = classes.cardRespondedClosed;
                } else if (isClosed && !hasResponded) {
                    cardClassName = classes.cardNotRespondedClosed;
                } else if (isClosed) {
                    cardClassName = classes.cardClosed;
                } else if (hasResponded) {
                    cardClassName = classes.cardResponded;
                } else {
                    cardClassName = classes.card;
                }
                
                return (
                    <Card 
                        key={ticket.number || Math.random()} 
                        className={cardClassName + ' topdesk-card'}
                        onClick={() => handleTicketClick(ticket.id)}
                        style={{ cursor: 'pointer' }} // Add pointer cursor to indicate clickability
                    >
                        <CardContent className={classes.cardContent}>
                            <div className={classes.cardHeader}>
                                <Typography className={classes.title} gutterBottom>
                                    {processSubject(ticket.request?.subject || ticket.request || '')}
                                </Typography>
                                <Typography className={classes.ticketNumber}>
                                    #{ticket.number || 'N/A'}
                                </Typography>
                            </div>
                            
                            <div style={{ flexGrow: 1 }}></div>
                            
                            <div className={classes.bottomRow}>
                                <Typography className={classes.categoryText}>
                                    {ticket.category?.name || 'No Category'} <br />
                                    {ticket.subcategory?.name || 'No Category'}
                                </Typography>
                                <Typography className={classes.dateText}>
                                    {formatDate(ticket.creationDate)}
                                </Typography>
                            </div>
                        </CardContent>
                    </Card>
                );
            })}
            
            <div className={classes.floatingMenu}>
                <Button 
                    variant="contained" 
                    className={classes.allRequestsButton}
                    onClick={handleAllRequestsClick}
                >
                    All Requests
                </Button>
                <Button 
                    variant="contained" 
                    className={classes.newRequestButton}
                    onClick={handleNewRequestClick}
                >
                    New Request
                </Button>
            </div>
        </div>
    );
}

// Wrap the component with the error boundary
export default function TopDesk() {
    return (
        <ErrorBoundary>
            <TopDeskCard />
        </ErrorBoundary>
    );
}
